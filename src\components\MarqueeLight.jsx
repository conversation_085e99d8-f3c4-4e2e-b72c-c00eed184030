import { useState, useEffect, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import { flushSync } from 'react-dom'
import './MarqueeLight.css'

const MarqueeLight = () => {
  const navigate = useNavigate()

  // 从localStorage加载设置的函数
  const loadSettings = () => {
    const savedSettings = localStorage.getItem('marquee-settings')
    if (savedSettings) {
      try {
        return JSON.parse(savedSettings)
      } catch (error) {
        console.error('Failed to parse saved settings:', error)
        return {}
      }
    }
    return {}
  }

  // 默认设置
  const defaultSettings = {
    backgroundColor: '#000000',
    textColor: '#ffffff',
    text: '欢迎使用走马灯功能 ✨ 这里可以显示任何文字内容',
    fontSize: 48,
    speed: 2,
    direction: 'left', // 线性滚动方向: left, right, up, down (移除对角线方向)
    brightness: 80,
    gap: 4,
    isCircular: false,
    circleRadius: 40,
    rotationAngle: 0,
    textRotation: 0
  }

  // 合并默认设置和保存的设置
  const initialSettings = { ...defaultSettings, ...loadSettings() }

  const [showControls, setShowControls] = useState(false)
  const [backgroundColor, setBackgroundColor] = useState(initialSettings.backgroundColor)
  const [textColor, setTextColor] = useState(initialSettings.textColor)
  const [text, setText] = useState(initialSettings.text)
  const [fontSize, setFontSize] = useState(initialSettings.fontSize)
  const [speed, setSpeed] = useState(initialSettings.speed)
  const [direction, setDirection] = useState(initialSettings.direction)
  const [brightness, setBrightness] = useState(initialSettings.brightness)
  const [gap, setGap] = useState(initialSettings.gap)
  const [isTransitioning, setIsTransitioning] = useState(false)
  const hideTimeoutRef = useRef(null)
  const controlsRef = useRef(null)
  const [showScrollHint, setShowScrollHint] = useState(false)
  const [scrollProgress, setScrollProgress] = useState(0)
  const [historyTexts, setHistoryTexts] = useState(() => {
    // 从 localStorage 加载历史记录
    const history = localStorage.getItem('marquee-history-texts')
    return history ? JSON.parse(history) : []
  })
  const marqueeKeyRef = useRef(0)
  // 添加一个状态来控制是否是初始加载
  const [isInitialLoad, setIsInitialLoad] = useState(true)
  // 添加新的状态变量
  const [isCircular, setIsCircular] = useState(initialSettings.isCircular)
  const [circleRadius, setCircleRadius] = useState(initialSettings.circleRadius)
  const [rotationAngle, setRotationAngle] = useState(initialSettings.rotationAngle)
  const [textRotation, setTextRotation] = useState(initialSettings.textRotation)

  // 添加控制面板关闭状态，用于防止立即重新触发
  const [isClosing, setIsClosing] = useState(false)

  // 添加用户交互状态跟踪
  const [isUserInteracting, setIsUserInteracting] = useState(false)
  const interactionTimeoutRef = useRef(null)

  // 监听设置变化并自动保存到localStorage
  useEffect(() => {
    saveSettings()
  }, [backgroundColor, textColor, text, fontSize, speed, direction, brightness, gap, isCircular, circleRadius, rotationAngle, textRotation])

  // 清理计时器
  useEffect(() => {
    return () => {
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current)
      }
      if (interactionTimeoutRef.current) {
        clearTimeout(interactionTimeoutRef.current)
      }
    }
  }, [])

  // 保存设置到localStorage的函数
  const saveSettings = () => {
    const settings = {
      backgroundColor,
      textColor,
      text,
      fontSize,
      speed,
      direction,
      brightness,
      gap,
      isCircular,
      circleRadius,
      rotationAngle,
      textRotation
    }
    localStorage.setItem('marquee-settings', JSON.stringify(settings))
  }

  // 预设文字内容
  const presetTexts = [
    { name: '欢迎信息', text: '欢迎光临！Welcome！' },
    { name: '节日祝福', text: '🎉 新年快乐！恭喜发财！🎊' },
    { name: '生日快乐', text: '🎂 生日快乐！Happy Birthday！🎈' },
    { name: '营业时间', text: '营业时间：09:00-21:00 欢迎光临' },
    { name: '促销信息', text: '🔥 限时优惠！全场8折！机会难得！' },
    { name: '温馨提示', text: '请保持安静 · 禁止吸烟 · 谢谢合作' },
    { name: '联系方式', text: '客服热线：400-123-4567 随时为您服务' },
    { name: '安全提醒', text: '⚠️ 注意安全 · 小心台阶 · 请勿奔跑' },
    { name: '感谢光临', text: '感谢您的光临！期待下次再见！👋' }
  ]

  // 预设颜色方案
  const colorSchemes = [
    { name: '经典黑白', bg: '#000000', text: '#ffffff' },
    { name: '红色警示', bg: '#dc2626', text: '#ffffff' },
    { name: '蓝色科技', bg: '#1e40af', text: '#ffffff' },
    { name: '绿色环保', bg: '#059669', text: '#ffffff' },
    { name: '橙色活力', bg: '#ea580c', text: '#ffffff' },
    { name: '紫色神秘', bg: '#7c3aed', text: '#ffffff' },
    { name: '黄色警告', bg: '#ca8a04', text: '#000000' },
    { name: '粉色温馨', bg: '#ec4899', text: '#ffffff' },
    { name: '青色清新', bg: '#0891b2', text: '#ffffff' }
  ]

  // 点击屏幕显示/隐藏控制界面
  const handleScreenClick = () => {
    // 如果正在关闭过程中，忽略点击
    if (isClosing) {
      return
    }

    if (showControls) {
      setIsClosing(true)
      setShowControls(false)
      // 设置一个短暂的延迟，防止立即重新触发
      setTimeout(() => {
        setIsClosing(false)
      }, 300) // 300ms延迟，与CSS过渡时间匹配
    } else {
      setShowControls(true)
      resetHideTimeout()
    }
  }

  // 处理用户交互开始
  const handleUserInteractionStart = () => {
    setIsUserInteracting(true)

    // 清除现有的自动隐藏计时器
    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current)
      hideTimeoutRef.current = null
    }

    // 清除交互结束的计时器
    if (interactionTimeoutRef.current) {
      clearTimeout(interactionTimeoutRef.current)
    }
  }

  // 处理用户交互结束（使用防抖机制）
  const handleUserInteractionEnd = () => {
    // 清除之前的交互结束计时器
    if (interactionTimeoutRef.current) {
      clearTimeout(interactionTimeoutRef.current)
    }

    // 设置2.5秒的防抖延迟，确保用户真正停止操作
    interactionTimeoutRef.current = setTimeout(() => {
      setIsUserInteracting(false)
      // 重新开始8秒自动关闭计时
      resetHideTimeout()
    }, 2500)
  }

  // 重置自动隐藏计时器（增强版）
  const resetHideTimeout = () => {
    // 如果用户正在交互，不启动自动关闭计时器
    if (isUserInteracting) {
      return
    }

    if (hideTimeoutRef.current) {
      clearTimeout(hideTimeoutRef.current)
    }

    hideTimeoutRef.current = setTimeout(() => {
      // 再次检查用户是否在交互，避免在交互过程中关闭
      if (!isUserInteracting) {
        setShowControls(false)
      }
    }, 8000) // 8秒后自动隐藏
  }

  // 监听控制界面显示状态
  useEffect(() => {
    if (showControls) {
      resetHideTimeout()
      setTimeout(() => {
        if (controlsRef.current) {
          const { scrollHeight, clientHeight } = controlsRef.current
          setShowScrollHint(scrollHeight > clientHeight)
        }
      }, 100)
    } else {
      setShowScrollHint(false)
    }

    return () => {
      if (hideTimeoutRef.current) {
        clearTimeout(hideTimeoutRef.current)
      }
    }
  }, [showControls])

  // 滚动事件处理（增强版，包含交互检测）
  const handleScroll = (e) => {
    const { scrollTop, scrollHeight, clientHeight } = e.target
    const progress = scrollTop / (scrollHeight - clientHeight)
    setScrollProgress(Math.min(Math.max(progress, 0), 1))

    if (showScrollHint && scrollTop > 50) {
      setShowScrollHint(false)
    }

    // 滚动时视为用户交互
    handleUserInteractionStart()
    handleUserInteractionEnd()
  }

  // 防止移动端页面滚动和缩放
  useEffect(() => {
    document.body.style.overflow = 'hidden'
    document.body.style.position = 'fixed'
    document.body.style.width = '100%'
    document.body.style.height = '100%'

    const preventZoom = (e) => {
      if (e.touches.length > 1) {
        e.preventDefault()
      }
    }

    const preventDoubleClick = (e) => {
      e.preventDefault()
    }

    document.addEventListener('touchstart', preventZoom, { passive: false })
    document.addEventListener('touchmove', preventZoom, { passive: false })
    document.addEventListener('dblclick', preventDoubleClick)

    return () => {
      document.body.style.overflow = ''
      document.body.style.position = ''
      document.body.style.width = ''
      document.body.style.height = ''

      document.removeEventListener('touchstart', preventZoom)
      document.removeEventListener('touchmove', preventZoom)
      document.removeEventListener('dblclick', preventDoubleClick)
    }
  }, [])

  // 添加到历史记录
  const addToHistory = (textContent) => {
    if (!textContent.trim()) return

    // 检查是否已存在相同文字
    const existingIndex = historyTexts.findIndex(item => item.text === textContent.trim())

    let updatedHistory
    if (existingIndex !== -1) {
      // 如果存在，移到最前面并更新时间
      const existingItem = { ...historyTexts[existingIndex], timestamp: new Date().toISOString() }
      updatedHistory = [existingItem, ...historyTexts.filter((_, index) => index !== existingIndex)]
    } else {
      // 如果不存在，添加新记录
      const newHistoryItem = {
        id: Date.now(),
        text: textContent.trim(),
        timestamp: new Date().toISOString()
      }
      updatedHistory = [newHistoryItem, ...historyTexts]
    }

    setHistoryTexts(updatedHistory)
    localStorage.setItem('marquee-history-texts', JSON.stringify(updatedHistory))
  }

  // 应用预设文字
  const applyPresetText = (preset) => {
    setIsTransitioning(true)
    setTimeout(() => {
      flushSync(() => {
        setText(preset.text)
        addToHistory(preset.text)
        marqueeKeyRef.current += 1
      })
      setIsTransitioning(false)
    }, 150)
    setTimeout(() => {
      setShowControls(false)
    }, 300)
  }

  // 应用颜色方案
  const applyColorScheme = (scheme) => {
    setIsTransitioning(true)
    setTimeout(() => {
      setBackgroundColor(scheme.bg)
      setTextColor(scheme.text)
      setIsTransitioning(false)
    }, 150)
    resetHideTimeout()
  }

  // 应用文字并添加到历史记录
  const applyText = (textContent) => {
    flushSync(() => {
      setText(textContent)
      addToHistory(textContent)
      marqueeKeyRef.current += 1
    })
  }

  // 删除历史记录
  const deleteHistoryText = (textId) => {
    const updatedHistory = historyTexts.filter(item => item.id !== textId)
    setHistoryTexts(updatedHistory)
    localStorage.setItem('marquee-history-texts', JSON.stringify(updatedHistory))
  }

  // 清空历史记录
  const clearHistory = () => {
    setHistoryTexts([])
    localStorage.removeItem('marquee-history-texts')
  }

  // 返回主页
  const handleBack = () => {
    navigate('/')
  }

  // 阻止控制面板点击事件冒泡（增强版，包含交互检测）
  const handleControlClick = (e) => {
    e.stopPropagation()
    handleUserInteractionStart()
    handleUserInteractionEnd()
  }

  const handleTouchStart = (e) => {
    e.preventDefault()
    handleScreenClick()
  }

  const handleControlTouch = (e) => {
    e.stopPropagation()
    handleUserInteractionStart()
    handleUserInteractionEnd()
  }

  // 获取线性滚动的CSS类名
  const getLinearDirectionClass = () => {
    return direction; // 只返回基本方向：left, right, up, down
  };

  // 动态计算需要渲染的文本片段数量，确保无缝且不会消失
  const getMarqueeFragments = () => {
    if (!isCircular) {
      // 线性布局逻辑
      const charCount = text.length || 1;

      // 更准确的文字宽度计算，考虑字体大小和字符类型
      const avgCharWidth = fontSize * 0.7; // 增加字符宽度估算
      const gapPx = (gap * window.innerWidth / 100);
      const textWidth = charCount * avgCharWidth + gapPx;

      // 根据方向计算需要的片段数量，确保有足够的冗余
      let minCount;
      if (direction === 'left' || direction === 'right') {
        // 水平滚动：确保有足够的片段覆盖屏幕宽度的3倍，避免裁切
        minCount = Math.max(Math.ceil(window.innerWidth * 3 / textWidth) + 3, 4);
      } else if (direction === 'up' || direction === 'down') {
        // 垂直滚动：确保有足够的片段覆盖屏幕高度的3倍
        const lineHeight = fontSize * 1.2; // 考虑行高
        minCount = Math.max(Math.ceil(window.innerHeight * 3 / lineHeight) + 3, 4);
      }

      const arr = [];
      for (let i = 0; i < minCount; i++) {
        arr.push(
          <span
            key={i}
            style={{
              marginRight: (direction === 'left' || direction === 'right') ? `${gap}vw` : 0,
              marginBottom: (direction === 'up' || direction === 'down') ? `${gap}vh` : 0,
              display: (direction === 'up' || direction === 'down') ? 'block' : 'inline-block',
              transform: `rotate(${textRotation}deg)`,
              transformOrigin: 'center center',
              // 确保文字不会被裁切
              whiteSpace: 'nowrap',
              flexShrink: 0
            }}
          >
            {text}
          </span>
        );
      }
      return arr;
    } else {
      // 圆形布局逻辑保持不变
      const segments = 12;
      const arr = [];
      for (let i = 0; i < segments; i++) {
        const angle = (i * 360 / segments);
        arr.push(
          <span
            key={i}
            className="circle-text-segment"
            style={{
              position: 'absolute',
              transform: `rotate(${angle}deg) translateY(-${circleRadius}vw) rotate(${textRotation}deg)`,
            }}
          >
            {text}
          </span>
        );
      }
      return arr;
    }
  };

  // 在组件挂载后设置初始加载状态
  useEffect(() => {
    // 设置一个短暂的延迟，确保DOM已完全渲染
    const timer = setTimeout(() => {
      setIsInitialLoad(false);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  // 添加方向变化监听，强制重置动画
  useEffect(() => {
    // 当方向改变时，强制重新渲染走马灯
    marqueeKeyRef.current += 1;
    // 短暂设置初始加载状态，确保文字立即可见
    setIsInitialLoad(true);
    setTimeout(() => {
      setIsInitialLoad(false);
    }, 50);
  }, [direction, isCircular]);

  // 添加文字旋转角度监听
  useEffect(() => {
    // 当文字旋转角度改变时，强制重新渲染走马灯
    marqueeKeyRef.current += 1;
  }, [textRotation]);

  return (
    <div
      className={`marquee-light ${isTransitioning ? 'transitioning' : ''}`}
      style={{
        backgroundColor: backgroundColor,
        opacity: brightness / 100
      }}
      onClick={handleScreenClick}
      onTouchStart={handleTouchStart}
    >
      {/* 走马灯文字 */}
      <div
        className="marquee-container"
        style={{
          position: 'relative',
          top: '50%',
          left: 0,
          width: (direction === 'left' || direction === 'right') ? '200vw' : '100%', // 水平滚动时扩展宽度
          height: isCircular ? '100vh' : (direction === 'up' || direction === 'down' ? '200vh' : 'auto'), // 垂直滚动时扩展高度
          transform: (direction === 'left' || direction === 'right') ? 'translate(-50vw, -50%)' : 'translateY(-50%)', // 水平滚动时调整起始位置
          overflow: 'visible', // 改为visible，避免裁切
          whiteSpace: (direction === 'up' || direction === 'down') ? 'normal' : 'nowrap',
          display: 'flex',
          justifyContent: (direction === 'left' || direction === 'right') ? 'flex-start' : 'center', // 水平滚动时左对齐
          alignItems: 'center',
          flexDirection: (direction === 'up' || direction === 'down') ? 'column' : 'row',
          padding: textRotation !== 0 ? `${Math.abs(Math.sin(textRotation * Math.PI / 180)) * fontSize}px` : '0'
        }}
      >
        <div
          key={marqueeKeyRef.current}
          className={`marquee-text ${isCircular ? 'circular' : getLinearDirectionClass()} ${isInitialLoad ? 'initial-load' : ''}`}
          style={{
            color: textColor,
            fontSize: `${fontSize}px`,
            animationDuration: `${(101 - speed) * 1}s`,
            whiteSpace: (direction === 'up' || direction === 'down') ? 'normal' : 'nowrap',
            display: isCircular ? 'flex' : (direction === 'up' || direction === 'down' ? 'block' : 'inline-block'),
            fontWeight: 'bold',
            textShadow: '2px 2px 4px rgba(0,0,0,0.5)',
            position: isCircular ? 'absolute' : 'relative',
            top: isCircular ? '50%' : 'auto',
            left: isCircular ? '50%' : 'auto',
            transform: isCircular ? `translate(-50%, -50%) rotate(${rotationAngle}deg)` : 'none',
            width: isCircular ? '0' : (direction === 'up' || direction === 'down' ? 'auto' : 'auto'),
            height: isCircular ? '0' : 'auto',
            justifyContent: 'center',
            alignItems: 'center',
            textAlign: (direction === 'up' || direction === 'down') ? 'center' : 'left'
          }}
        >
          {getMarqueeFragments()}
        </div>
      </div>

      {/* 控制界面 */}
      <div
        ref={controlsRef}
        className={`controls-overlay ${showControls ? 'visible' : ''}`}
        onClick={handleControlClick}
        onTouchStart={handleControlTouch}
        onScroll={handleScroll}
      >
        <div className="controls-content">
          {/* 顶部控制栏 */}
          <div className="top-controls">
            <button className="back-button" onClick={handleBack}>
              <span className="back-icon">←</span>
              返回
            </button>
            <h2 className="mode-title">走马灯</h2>
            <div className="top-right-controls">
              <div className="brightness-control">
                <span className="brightness-icon">☀️</span>
                <input
                  type="range"
                  min="10"
                  max="100"
                  value={brightness}
                  onChange={(e) => {
                    setBrightness(e.target.value)
                    handleUserInteractionStart()
                    handleUserInteractionEnd()
                  }}
                  onMouseDown={handleUserInteractionStart}
                  onMouseUp={handleUserInteractionEnd}
                  onTouchStart={handleUserInteractionStart}
                  onTouchEnd={handleUserInteractionEnd}
                  className="brightness-slider"
                />
                <span className="brightness-value">{brightness}%</span>
              </div>
              <button
                className="close-button"
                onClick={() => {
                  setShowControls(false)
                  handleUserInteractionStart()
                  handleUserInteractionEnd()
                }}
              >
                <span className="close-icon">×</span>
              </button>
            </div>
          </div>

          {/* 主内容区域 */}
          <div className="main-content">
            {/* 文字输入区域 */}
            <div className="text-input-section">
              <h3>文字内容</h3>
              <div className="text-input-container">
                <textarea
                  value={text}
                  onChange={(e) => {
                    setText(e.target.value)
                    if (e.target.value.trim()) {
                      addToHistory(e.target.value)
                    }
                    handleUserInteractionStart()
                    handleUserInteractionEnd()
                  }}
                  onFocus={handleUserInteractionStart}
                  onBlur={handleUserInteractionEnd}
                  onInput={() => {
                    handleUserInteractionStart()
                    handleUserInteractionEnd()
                  }}
                  placeholder="请输入要显示的文字内容..."
                  className="text-input"
                  rows="3"
                />
              </div>
            </div>

            {/* 样式控制 */}
            <div className="style-controls">
              <div className="control-group">
                <label>字体大小: {fontSize}px</label>
                <input
                  type="range"
                  min="24"
                  max="120"
                  value={fontSize}
                  onChange={(e) => {
                    setFontSize(e.target.value)
                    handleUserInteractionStart()
                    handleUserInteractionEnd()
                  }}
                  onMouseDown={handleUserInteractionStart}
                  onMouseUp={handleUserInteractionEnd}
                  onTouchStart={handleUserInteractionStart}
                  onTouchEnd={handleUserInteractionEnd}
                  className="control-slider"
                />
              </div>

              <div className="control-group">
                <label>滚动速度: {speed}%</label>
                <input
                  type="range"
                  min="10"
                  max="100"
                  value={speed}
                  onChange={(e) => {
                    setSpeed(e.target.value)
                    handleUserInteractionStart()
                    handleUserInteractionEnd()
                  }}
                  onMouseDown={handleUserInteractionStart}
                  onMouseUp={handleUserInteractionEnd}
                  onTouchStart={handleUserInteractionStart}
                  onTouchEnd={handleUserInteractionEnd}
                  className="control-slider"
                />
              </div>

              <div className="control-group">
                <label>间隔距离: {gap}vw</label>
                <input
                  type="range"
                  min="1"
                  max="20"
                  step="1"
                  value={gap}
                  onChange={e => {
                    setGap(Number(e.target.value))
                    handleUserInteractionStart()
                    handleUserInteractionEnd()
                  }}
                  onMouseDown={handleUserInteractionStart}
                  onMouseUp={handleUserInteractionEnd}
                  onTouchStart={handleUserInteractionStart}
                  onTouchEnd={handleUserInteractionEnd}
                  className="control-slider"
                />
              </div>

              {/* 新增：滚动模式选择 */}
              <div className="control-group">
                <label>滚动模式</label>
                <div className="mode-buttons">
                  <button
                    className={`mode-btn ${!isCircular ? 'active' : ''}`}
                    onClick={() => {
                      setIsCircular(false)
                      handleUserInteractionStart()
                      handleUserInteractionEnd()
                    }}
                  >
                    线性滚动
                  </button>
                  <button
                    className={`mode-btn ${isCircular ? 'active' : ''}`}
                    onClick={() => {
                      setIsCircular(true)
                      handleUserInteractionStart()
                      handleUserInteractionEnd()
                    }}
                  >
                    圆形旋转
                  </button>
                </div>
              </div>

              {/* 线性模式的方向控制 - 简化版 */}
              {!isCircular && (
                <div className="control-group">
                  <label>滚动方向</label>
                  <div className="direction-grid">
                    <button
                      className={`direction-btn ${direction === 'left' ? 'active' : ''}`}
                      onClick={() => {
                        setDirection('left')
                        handleUserInteractionStart()
                        handleUserInteractionEnd()
                      }}
                    >
                      ← 向左
                    </button>
                    <button
                      className={`direction-btn ${direction === 'right' ? 'active' : ''}`}
                      onClick={() => {
                        setDirection('right')
                        handleUserInteractionStart()
                        handleUserInteractionEnd()
                      }}
                    >
                      向右 →
                    </button>
                    <button
                      className={`direction-btn ${direction === 'up' ? 'active' : ''}`}
                      onClick={() => {
                        setDirection('up')
                        handleUserInteractionStart()
                        handleUserInteractionEnd()
                      }}
                    >
                      ↑ 向上
                    </button>
                    <button
                      className={`direction-btn ${direction === 'down' ? 'active' : ''}`}
                      onClick={() => {
                        setDirection('down')
                        handleUserInteractionStart()
                        handleUserInteractionEnd()
                      }}
                    >
                      向下 ↓
                    </button>
                  </div>
                </div>
              )}



              {/* 文字旋转控制 - 两种模式都可用 */}
              <div className="control-group">
                <label>文字角度: {textRotation}°</label>
                <div className="angle-selector">
                  <div className="text-rotation-preview">
                    <span style={{ transform: `rotate(${textRotation}deg)` }}>文</span>
                  </div>
                  <input
                    type="range"
                    min="0"
                    max="359"
                    value={textRotation}
                    onChange={(e) => {
                      setTextRotation(Number(e.target.value))
                      handleUserInteractionStart()
                      handleUserInteractionEnd()
                    }}
                    onMouseDown={handleUserInteractionStart}
                    onMouseUp={handleUserInteractionEnd}
                    onTouchStart={handleUserInteractionStart}
                    onTouchEnd={handleUserInteractionEnd}
                    className="control-slider angle-slider"
                  />
                </div>
              </div>

              {/* 圆形模式的控制选项 */}
              {isCircular && (
                <>
                  <div className="control-group">
                    <label>圆形半径: {circleRadius}vw</label>
                    <input
                      type="range"
                      min="20"
                      max="60"
                      value={circleRadius}
                      onChange={(e) => {
                        setCircleRadius(Number(e.target.value))
                        handleUserInteractionStart()
                        handleUserInteractionEnd()
                      }}
                      onMouseDown={handleUserInteractionStart}
                      onMouseUp={handleUserInteractionEnd}
                      onTouchStart={handleUserInteractionStart}
                      onTouchEnd={handleUserInteractionEnd}
                      className="control-slider"
                    />
                  </div>

                  <div className="control-group">
                    <label>旋转方向: {rotationAngle}°</label>
                    <div className="angle-selector">
                      <div className="angle-preview">
                        <div
                          className="angle-indicator"
                          style={{ transform: `rotate(${rotationAngle}deg)` }}
                        ></div>
                      </div>
                      <input
                        type="range"
                        min="0"
                        max="359"
                        value={rotationAngle}
                        onChange={(e) => {
                          setRotationAngle(Number(e.target.value))
                          handleUserInteractionStart()
                          handleUserInteractionEnd()
                        }}
                        onMouseDown={handleUserInteractionStart}
                        onMouseUp={handleUserInteractionEnd}
                        onTouchStart={handleUserInteractionStart}
                        onTouchEnd={handleUserInteractionEnd}
                        className="control-slider angle-slider"
                      />
                    </div>
                  </div>
                </>
              )}
            </div>

            {/* 颜色控制 */}
            <div className="color-controls">
              <h4>颜色设置</h4>
              <div className="color-inputs">
                <div className="color-input-group">
                  <label>背景颜色</label>
                  <input
                    type="color"
                    value={backgroundColor}
                    onChange={(e) => {
                      setBackgroundColor(e.target.value)
                      handleUserInteractionStart()
                      handleUserInteractionEnd()
                    }}
                    onFocus={handleUserInteractionStart}
                    onBlur={handleUserInteractionEnd}
                    className="color-picker"
                  />
                  <span className="color-value">{backgroundColor.toUpperCase()}</span>
                </div>
                <div className="color-input-group">
                  <label>文字颜色</label>
                  <input
                    type="color"
                    value={textColor}
                    onChange={(e) => {
                      setTextColor(e.target.value)
                      handleUserInteractionStart()
                      handleUserInteractionEnd()
                    }}
                    onFocus={handleUserInteractionStart}
                    onBlur={handleUserInteractionEnd}
                    className="color-picker"
                  />
                  <span className="color-value">{textColor.toUpperCase()}</span>
                </div>
              </div>

              {/* 颜色方案 */}
              <div className="color-schemes">
                <h5>快速配色</h5>
                <div className="schemes-grid">
                  {colorSchemes.map((scheme, index) => (
                    <div
                      key={index}
                      className={`scheme-item ${backgroundColor === scheme.bg && textColor === scheme.text ? 'active' : ''}`}
                      style={{ backgroundColor: scheme.bg, color: scheme.text }}
                      onClick={() => {
                        applyColorScheme(scheme)
                        handleUserInteractionStart()
                        handleUserInteractionEnd()
                      }}
                    >
                      <span className="scheme-name">{scheme.name}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* 分栏布局：预设和历史记录 */}
            <div className="texts-columns">
              {/* 预设文字 */}
              <div className="texts-column">
                <h4>预设文字</h4>
                <div className="preset-texts-grid">
                  {presetTexts.map((preset, index) => (
                    <div
                      key={index}
                      className={`preset-text-item ${text === preset.text ? 'active' : ''}`}
                      onClick={() => {
                        applyPresetText(preset)
                        handleUserInteractionStart()
                        handleUserInteractionEnd()
                      }}
                    >
                      <span className="preset-text-name">{preset.name}</span>
                      <span className="preset-text-preview">{preset.text}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* 历史记录 */}
              <div className="texts-column">
                <div className="history-header">
                  <h4>历史记录 ({historyTexts.length})</h4>
                  {historyTexts.length > 0 && (
                    <button
                      className="clear-history-btn"
                      onClick={() => {
                        clearHistory()
                        handleUserInteractionStart()
                        handleUserInteractionEnd()
                      }}
                    >
                      清空
                    </button>
                  )}
                </div>
                {historyTexts.length > 0 ? (
                  <div className="history-texts-grid">
                    {historyTexts.map((item) => (
                      <div
                        key={item.id}
                        className={`history-text-item ${text === item.text ? 'active' : ''}`}
                        onClick={() => {
                          applyText(item.text)
                          handleUserInteractionStart()
                          handleUserInteractionEnd()
                        }}
                      >
                        <span className="history-text-preview">{item.text}</span>
                        <span className="history-text-time">
                          {new Date(item.timestamp).toLocaleString('zh-CN', {
                            month: 'short',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </span>
                        <button
                          className="delete-text-btn"
                          onClick={(e) => {
                            e.stopPropagation()
                            deleteHistoryText(item.id)
                            handleUserInteractionStart()
                            handleUserInteractionEnd()
                          }}
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="empty-history">
                    <p>暂无历史记录</p>
                    <p>输入文字后自动记录</p>
                  </div>
                )}
              </div>
            </div>
          </div>


        </div>

        {/* 滚动指示器 */}
        <div className="scroll-indicator">
          <div
            className="scroll-thumb"
            style={{
              height: `${Math.max(scrollProgress * 100, 10)}%`,
              transform: `translateY(${scrollProgress * (100 - Math.max(scrollProgress * 100, 10))}px)`
            }}
          />
        </div>

        {/* 滚动提示 */}
        {showScrollHint && (
          <div className="scroll-hint">
            ↓ 向下滑动查看更多
          </div>
        )}
      </div>
    </div>
  )
}

export default MarqueeLight
