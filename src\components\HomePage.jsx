import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import '../App.css'

function HomePage() {
  const navigate = useNavigate()
  const [selectedMode, setSelectedMode] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const [animateCards, setAnimateCards] = useState(false)

  const lightModes = [
    {
      id: 'ambient',
      title: '氛围灯',
      description: '营造温馨舒适的环境氛围',
      icon: '🌙',
      color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      features: ['多种颜色模式', '亮度调节', '定时开关'],
      path: '/ambient',
      status: 'available'
    },
    {
      id: 'solid',
      title: '纯色灯',
      description: '单一颜色的稳定照明',
      icon: '💡',
      color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      features: ['RGB调色', '亮度控制', '颜色预设'],
      path: '/solid',
      status: 'available'
    },
    {
      id: 'marquee',
      title: '走马灯',
      description: '动态流光效果展示',
      icon: '✨',
      color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      features: ['流光效果', '速度调节', '方向控制'],
      path: '/marquee',
      status: 'available'
    }
  ]

  // 页面加载动画
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false)
      setTimeout(() => setAnimateCards(true), 100)
    }, 500)
    return () => clearTimeout(timer)
  }, [])

  const handleModeClick = (mode) => {
    if (mode.status === 'available') {
      // 添加点击动画效果
      const card = document.querySelector(`[data-mode-id="${mode.id}"]`)
      if (card) {
        card.classList.add('clicking')
        setTimeout(() => {
          navigate(mode.path)
        }, 200)
      }
    } else {
      // 其他功能暂未实现，显示选中状态
      setSelectedMode(selectedMode === mode.id ? null : mode.id)
    }
  }

  // 加载状态组件
  if (isLoading) {
    return (
      <div className="app loading">
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p className="loading-text">光效控制系统启动中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="app">
      <header className="header">
        <div className="header-content">
          <div className="title-container">
            <span className="title-icon">💫</span>
            <div className="title-text">
              <h1 className="title">光效控制系统</h1>
              <p className="subtitle">智能灯光控制，打造完美光影体验</p>
            </div>
          </div>
        </div>
      </header>

      <main className="main">
        <div className={`modes-grid ${animateCards ? 'animate' : ''}`}>
          {lightModes.map((mode, index) => (
            <div
              key={mode.id}
              data-mode-id={mode.id}
              className={`mode-card ${selectedMode === mode.id ? 'selected' : ''} ${mode.status === 'available' ? 'available' : ''}`}
              onClick={() => handleModeClick(mode)}
              style={{
                '--card-gradient': mode.color,
                '--animation-delay': `${index * 0.1}s`
              }}
            >
              <div className="mode-card-inner">
                <div className="mode-header">
                  <div className="mode-icon-container">
                    <span className="mode-icon">{mode.icon}</span>
                  </div>
                  <div className="mode-title-container">
                    <h3 className="mode-title">{mode.title}</h3>
                    {mode.status === 'available' && (
                      <span className="available-badge">
                        <span className="badge-dot"></span>
                        可用
                      </span>
                    )}
                  </div>
                </div>

                <p className="mode-description">{mode.description}</p>

                <div className="mode-features">
                  {mode.features.map((feature, featureIndex) => (
                    <span key={featureIndex} className="feature-tag">
                      {feature}
                    </span>
                  ))}
                </div>

                <div className="mode-footer">
                  <div className="mode-status">
                    <span className={`status-indicator ${mode.status}`}></span>
                    <span className="status-text">
                      {mode.status === 'available'
                        ? '点击进入'
                        : selectedMode === mode.id
                          ? '已选中'
                          : '点击选择'
                      }
                    </span>
                  </div>
                  <div className="mode-arrow">
                    <span>→</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {selectedMode && !lightModes.find(m => m.id === selectedMode)?.status === 'available' && (
          <div className="control-panel">
            <div className="control-panel-header">
              <h4>控制面板</h4>
              <button
                className="close-panel-btn"
                onClick={() => setSelectedMode(null)}
              >
                ✕
              </button>
            </div>
            <div className="control-panel-content">
              <div className="coming-soon">
                <span className="coming-soon-icon">🚧</span>
                <div className="coming-soon-text">
                  <h5>功能开发中</h5>
                  <p>该功能即将上线，敬请期待...</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </main>

      <footer className="footer">
        <div className="footer-content">
          <p>&copy; 2024 光效控制系统</p>
          <p className="footer-subtitle">让光影更智能</p>
        </div>
      </footer>
    </div>
  )
}

export default HomePage
