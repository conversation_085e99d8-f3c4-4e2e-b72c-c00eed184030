/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 应用主容器 */
.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
  color: #ffffff;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  overflow-x: hidden;
}

/* 头部样式 */
.header {
  padding: 3rem 2rem;
  text-align: center;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-content {
  max-width: 800px;
  margin: 0 auto;
}

.title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.title-icon {
  font-size: 3.5rem;
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    text-shadow: 0 0 20px #667eea, 0 0 30px #667eea, 0 0 40px #667eea;
  }
  to {
    text-shadow: 0 0 30px #764ba2, 0 0 40px #764ba2, 0 0 50px #764ba2;
  }
}

.subtitle {
  font-size: 1.2rem;
  color: #b0b0b0;
  font-weight: 300;
}

/* 主内容区域 */
.main {
  padding: 4rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

/* 模式网格 */
.modes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

/* 模式卡片 */
.mode-card {
  background: rgba(255, 255, 255, 0.08);
  border-radius: 20px;
  padding: 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.mode-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--card-gradient);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.mode-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.2);
}

.mode-card:hover::before {
  opacity: 1;
}

.mode-card.selected {
  border-color: #667eea;
  box-shadow: 0 0 30px rgba(102, 126, 234, 0.3);
  transform: translateY(-5px);
}

.mode-card.selected::before {
  opacity: 1;
}

.mode-card.available {
  border-color: rgba(74, 222, 128, 0.3);
}

.mode-card.available:hover {
  border-color: rgba(74, 222, 128, 0.5);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 20px rgba(74, 222, 128, 0.2);
}

/* 模式头部 */
.mode-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  position: relative;
}

.mode-icon {
  font-size: 2.5rem;
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
}

.mode-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #ffffff;
  flex: 1;
}

.available-badge {
  background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(74, 222, 128, 0.3);
  animation: pulse-green 2s infinite;
}

@keyframes pulse-green {
  0%, 100% {
    box-shadow: 0 2px 8px rgba(74, 222, 128, 0.3);
  }
  50% {
    box-shadow: 0 4px 16px rgba(74, 222, 128, 0.5);
  }
}

/* 模式描述 */
.mode-description {
  color: #b0b0b0;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

/* 功能标签 */
.mode-features {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

.feature-tag {
  background: rgba(255, 255, 255, 0.1);
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.85rem;
  color: #e0e0e0;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 模式状态 */
.mode-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: auto;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #4ade80;
  animation: pulse 2s infinite;
}

.mode-card.selected .status-indicator {
  background: #667eea;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.status-text {
  font-size: 0.9rem;
  color: #a0a0a0;
}

/* 控制面板 */
.control-panel {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 15px;
  padding: 2rem;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.control-panel h4 {
  font-size: 1.3rem;
  margin-bottom: 1rem;
  color: #667eea;
}

.coming-soon {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 1rem;
  padding: 1rem;
  background: rgba(255, 193, 7, 0.1);
  border-radius: 10px;
  border: 1px solid rgba(255, 193, 7, 0.3);
}

.coming-soon span:first-child {
  font-size: 1.2rem;
}

/* 页脚 */
.footer {
  text-align: center;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.3);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  color: #888;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .title {
    font-size: 2rem;
    flex-direction: column;
    gap: 0.5rem;
  }

  .title-icon {
    font-size: 2.5rem;
  }

  .modes-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .main {
    padding: 2rem 1rem;
  }

  .header {
    padding: 2rem 1rem;
  }
}
