/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 应用主容器 */
.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 30%, #16213e 70%, #0f3460 100%);
  color: #ffffff;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  overflow-x: hidden;
  position: relative;
  /* 移动端安全区域适配 */
  padding-top: env(safe-area-inset-top);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: env(safe-area-inset-left);
  padding-right: env(safe-area-inset-right);
}

/* 加载状态样式 */
.app.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
}

.loading-container {
  text-align: center;
  animation: fadeIn 0.5s ease-in-out;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1.5rem;
}

.loading-text {
  font-size: 1.1rem;
  color: #b0b0b0;
  font-weight: 300;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 头部样式 */
.header {
  padding: 2.5rem 2rem;
  text-align: center;
  background: rgba(255, 255, 255, 0.03);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  position: relative;
  overflow: hidden;
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  opacity: 0.5;
  z-index: -1;
}

.header-content {
  max-width: 900px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

.title-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 1rem;
}

.title-icon {
  font-size: 4rem;
  animation: glow 3s ease-in-out infinite alternate;
  filter: drop-shadow(0 0 20px rgba(102, 126, 234, 0.6));
}

.title-text {
  text-align: left;
}

.title {
  font-size: 2.8rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
}

@keyframes glow {
  from {
    text-shadow: 0 0 20px #667eea, 0 0 30px #667eea, 0 0 40px #667eea;
    filter: drop-shadow(0 0 20px rgba(102, 126, 234, 0.6));
  }
  to {
    text-shadow: 0 0 30px #764ba2, 0 0 40px #764ba2, 0 0 50px #764ba2;
    filter: drop-shadow(0 0 30px rgba(118, 75, 162, 0.8));
  }
}

.subtitle {
  font-size: 1.1rem;
  color: #c0c0c0;
  font-weight: 300;
  opacity: 0.9;
  line-height: 1.4;
}

/* 主内容区域 */
.main {
  padding: 3rem 2rem;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
}

/* 模式网格 */
.modes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 2.5rem;
  margin-bottom: 3rem;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease;
}

.modes-grid.animate {
  opacity: 1;
  transform: translateY(0);
}

/* 模式卡片 */
.mode-card {
  background: rgba(255, 255, 255, 0.06);
  border-radius: 24px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
  opacity: 0;
  transform: translateY(20px);
  animation: cardSlideIn 0.6s ease forwards;
  animation-delay: var(--animation-delay);
  /* 移动端优化 */
  min-height: 280px;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

@keyframes cardSlideIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.mode-card-inner {
  padding: 2rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 2;
}

.mode-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--card-gradient);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.mode-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--card-gradient);
  opacity: 0;
  transition: opacity 0.4s ease;
  z-index: 1;
}

.mode-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4), 0 0 30px rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.15);
}

.mode-card:hover::before {
  opacity: 1;
}

.mode-card:hover::after {
  opacity: 0.05;
}

.mode-card.clicking {
  transform: translateY(-4px) scale(0.98);
  transition: all 0.2s ease;
}

.mode-card.selected {
  border-color: #667eea;
  box-shadow: 0 0 40px rgba(102, 126, 234, 0.4);
  transform: translateY(-8px);
}

.mode-card.selected::before {
  opacity: 1;
}

.mode-card.available {
  border-color: rgba(74, 222, 128, 0.2);
}

.mode-card.available:hover {
  border-color: rgba(74, 222, 128, 0.4);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4), 0 0 30px rgba(74, 222, 128, 0.3);
}

/* 模式头部 */
.mode-header {
  display: flex;
  align-items: flex-start;
  gap: 1.2rem;
  margin-bottom: 1.5rem;
  position: relative;
}

.mode-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.mode-card:hover .mode-icon-container {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.mode-icon {
  font-size: 2rem;
  filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.4));
  transition: all 0.3s ease;
}

.mode-title-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.mode-title {
  font-size: 1.4rem;
  font-weight: 600;
  color: #ffffff;
  line-height: 1.3;
  margin: 0;
}

.available-badge {
  background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
  color: white;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 3px 12px rgba(74, 222, 128, 0.4);
  animation: pulse-green 3s infinite;
  display: flex;
  align-items: center;
  gap: 0.4rem;
  width: fit-content;
  border: 1px solid rgba(74, 222, 128, 0.3);
}

.badge-dot {
  width: 6px;
  height: 6px;
  background: white;
  border-radius: 50%;
  animation: pulse-dot 2s infinite;
}

@keyframes pulse-green {
  0%, 100% {
    box-shadow: 0 3px 12px rgba(74, 222, 128, 0.4);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 5px 20px rgba(74, 222, 128, 0.6);
    transform: scale(1.02);
  }
}

@keyframes pulse-dot {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 模式描述 */
.mode-description {
  color: #c0c0c0;
  margin-bottom: 1.5rem;
  line-height: 1.6;
  font-size: 0.95rem;
  opacity: 0.9;
}

/* 功能标签 */
.mode-features {
  display: flex;
  flex-wrap: wrap;
  gap: 0.6rem;
  margin-bottom: 2rem;
  flex: 1;
}

.feature-tag {
  background: rgba(255, 255, 255, 0.08);
  padding: 0.4rem 0.9rem;
  border-radius: 18px;
  font-size: 0.8rem;
  color: #e8e8e8;
  border: 1px solid rgba(255, 255, 255, 0.12);
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.mode-card:hover .feature-tag {
  background: rgba(255, 255, 255, 0.12);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

/* 模式底部 */
.mode-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: auto;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.08);
}

.mode-status {
  display: flex;
  align-items: center;
  gap: 0.6rem;
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #4ade80;
  animation: pulse 2s infinite;
  box-shadow: 0 0 10px rgba(74, 222, 128, 0.5);
}

.status-indicator.available {
  background: #4ade80;
  box-shadow: 0 0 10px rgba(74, 222, 128, 0.5);
}

.mode-card.selected .status-indicator {
  background: #667eea;
  box-shadow: 0 0 10px rgba(102, 126, 234, 0.5);
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.status-text {
  font-size: 0.85rem;
  color: #b0b0b0;
  font-weight: 500;
}

.mode-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 50%;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.mode-arrow span {
  font-size: 1rem;
  color: #ffffff;
  transition: all 0.3s ease;
}

.mode-card:hover .mode-arrow {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateX(3px);
}

.mode-card.available:hover .mode-arrow {
  background: rgba(74, 222, 128, 0.2);
  border-color: rgba(74, 222, 128, 0.3);
}

/* 控制面板 */
.control-panel {
  background: rgba(255, 255, 255, 0.04);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  overflow: hidden;
  animation: slideUp 0.4s ease;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.control-panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  background: rgba(255, 255, 255, 0.02);
}

.control-panel-header h4 {
  font-size: 1.2rem;
  margin: 0;
  color: #ffffff;
  font-weight: 600;
}

.close-panel-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  /* 移动端优化 */
  min-width: 44px;
  min-height: 44px;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
}

.close-panel-btn:hover {
  background: rgba(255, 0, 0, 0.2);
  border-color: rgba(255, 0, 0, 0.4);
  transform: scale(1.1);
}

.control-panel-content {
  padding: 2rem;
}

.coming-soon {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 193, 7, 0.08);
  border-radius: 16px;
  border: 1px solid rgba(255, 193, 7, 0.2);
  backdrop-filter: blur(10px);
}

.coming-soon-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.coming-soon-text h5 {
  font-size: 1.1rem;
  color: #ffffff;
  margin: 0 0 0.5rem 0;
  font-weight: 600;
}

.coming-soon-text p {
  font-size: 0.9rem;
  color: #c0c0c0;
  margin: 0;
  opacity: 0.8;
}

/* 页脚 */
.footer {
  text-align: center;
  padding: 2.5rem 2rem;
  background: rgba(0, 0, 0, 0.2);
  border-top: 1px solid rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(10px);
}

.footer-content p {
  margin: 0;
  color: #888;
  font-size: 0.9rem;
}

.footer-subtitle {
  margin-top: 0.3rem !important;
  font-size: 0.8rem !important;
  color: #666 !important;
  opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app {
    /* 移动端安全区域适配增强 */
    padding-top: max(env(safe-area-inset-top), 0px);
    padding-bottom: max(env(safe-area-inset-bottom), 0px);
    padding-left: max(env(safe-area-inset-left), 0px);
    padding-right: max(env(safe-area-inset-right), 0px);
  }

  .header {
    padding: 2rem 1.5rem;
  }

  .title-container {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .title-text {
    text-align: center;
  }

  .title {
    font-size: 2.2rem;
  }

  .title-icon {
    font-size: 3rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .main {
    padding: 2rem 1.5rem;
  }

  .modes-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
    max-width: 500px;
    margin: 0 auto 3rem;
  }

  .mode-card {
    min-height: 260px;
    /* 增大移动端触摸目标 */
    touch-action: manipulation;
  }

  .mode-card-inner {
    padding: 1.5rem;
  }

  .mode-header {
    gap: 1rem;
  }

  .mode-icon-container {
    width: 50px;
    height: 50px;
  }

  .mode-icon {
    font-size: 1.6rem;
  }

  .mode-title {
    font-size: 1.2rem;
  }

  .available-badge {
    font-size: 0.65rem;
    padding: 0.3rem 0.6rem;
  }

  .mode-description {
    font-size: 0.9rem;
    margin-bottom: 1.2rem;
  }

  .feature-tag {
    font-size: 0.75rem;
    padding: 0.3rem 0.7rem;
  }

  .mode-arrow {
    width: 28px;
    height: 28px;
  }

  .mode-arrow span {
    font-size: 0.9rem;
  }

  .control-panel-header {
    padding: 1rem 1.5rem;
  }

  .control-panel-content {
    padding: 1.5rem;
  }

  .coming-soon {
    flex-direction: column;
    text-align: center;
    gap: 0.8rem;
    padding: 1.2rem;
  }

  .footer {
    padding: 2rem 1.5rem;
  }
}

/* 小屏幕设备优化 */
@media (max-width: 480px) {
  .header {
    padding: 1.5rem 1rem;
  }

  .title {
    font-size: 1.8rem;
  }

  .title-icon {
    font-size: 2.5rem;
  }

  .main {
    padding: 1.5rem 1rem;
  }

  .modes-grid {
    gap: 1.5rem;
  }

  .mode-card {
    min-height: 240px;
  }

  .mode-card-inner {
    padding: 1.2rem;
  }

  .mode-icon-container {
    width: 45px;
    height: 45px;
  }

  .mode-icon {
    font-size: 1.4rem;
  }

  .mode-title {
    font-size: 1.1rem;
  }

  .available-badge {
    font-size: 0.6rem;
    padding: 0.25rem 0.5rem;
  }

  .feature-tag {
    font-size: 0.7rem;
    padding: 0.25rem 0.6rem;
  }

  .control-panel-header {
    padding: 0.8rem 1rem;
  }

  .control-panel-content {
    padding: 1rem;
  }

  .footer {
    padding: 1.5rem 1rem;
  }

  .footer-content p {
    font-size: 0.8rem;
  }
}

/* 超大屏幕优化 */
@media (min-width: 1400px) {
  .modes-grid {
    grid-template-columns: repeat(3, 1fr);
    max-width: 1200px;
    margin: 0 auto 3rem;
  }
}

/* 横屏模式优化 */
@media (max-height: 600px) and (orientation: landscape) {
  .header {
    padding: 1rem 2rem;
  }

  .title-container {
    flex-direction: row;
    gap: 1rem;
  }

  .title {
    font-size: 1.8rem;
  }

  .title-icon {
    font-size: 2rem;
  }

  .main {
    padding: 1.5rem 2rem;
  }

  .mode-card {
    min-height: 200px;
  }
}
